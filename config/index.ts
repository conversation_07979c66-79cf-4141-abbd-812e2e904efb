import dev from "./dev.json";
import prod from "./prod.json";

export interface Config {
  title: string;
  appUrl: string;
  apiUrl: string;
  newOrderUrl: string;
  apiExternalUrl: string;
  referralApiUrl: string;
  appInsightUrl: string;
  walletApiUrl: string;
  alertApiUrl: string;
  authorizeApiUrl: string;
  userApiUrl: string;
  webhookApiUrl: string;
  domainNameApiUrl: string;
  endpoints: {
    ws: string;
  };
  network: string;
  rpcUrl: string;
  explorerSuiUrl: string;
  link_telegram: string;
  linkSocial: {
    twitter: string;
    telegram: string;
    discord: string;
  };
  customerSupportUrl: string;
  developerUrl: string;
  treasuryAddress: string;
  homePage: {
    link: {
      telegramPremiumSignal: string;
      buyBot: string;
      airdrop: string;
      developer: string;
      userGuide: string;
      apiDocs: string;
      termsOfUse: string;
      policy: string;
      roadMap: string;
      brandKit: string;
      customerSupport: string;
    };
  };
  verifyToken: {
    limited: {
      price: number;
    };
    standard: {
      price: number;
    };
  };
  sponsorAddress: string;
  minGasFee: number;
  turnkeyOrganizationId: string;
}

export const envConfig = process.env.NEXT_PUBLIC_ENV || "prod";

interface EnvConfig {
  prod: Config;
  dev: Config;
  beta: Config;
}

const configs: EnvConfig = { dev, prod } as EnvConfig;
const config: Config = configs[envConfig as keyof typeof configs];

export default config;
