"use client";

import React, { useState, useEffect } from "react";
import { TurnkeyProvider } from "@turnkey/sdk-react";
import config from "@/config";

export default function TurnkeyProviderWrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Render children without TurnkeyProvider during SSR
  if (!isClient || !config) {
    return <>{children}</>;
  }

  return (
    <TurnkeyProvider
      config={{
        defaultOrganizationId: config.turnkeyOrganizationId,
        apiBaseUrl: "https://api.turnkey.com",
      }}
    >
      {children}
    </TurnkeyProvider>
  );
}
