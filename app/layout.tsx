import type { Metada<PERSON>, Viewport } from "next";
import "./globals.css";
import { AppProvider } from "@/app/provider";
import "rc-tooltip/assets/bootstrap.css";
import "react-datepicker/dist/react-datepicker.css";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { BasePage } from "../layouts/BasePage";
import Script from "next/script";
import ClearServiceWorker from "@/components/ClearServiceWorker";
import TurnkeyProviderWrapper from "@/app/TurnkeyProviderWrapper";
import "@turnkey/sdk-react/styles";

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  userScalable: false,
  maximumScale: 1,
  viewportFit: "cover",
  themeColor: "#000000",
};

export const metadata: Metadata = {
  title: "RaidenX | The best trading terminal on SUI",
  description:
    "Fastest trading & sniping bot on SUI, 1-click buy & sell, realtime chart, audit results, limit orders, DCA, copy trade - only on RaidenX",
  icons: {
    icon: "/raidenx.png",
    apple: "/raidenx.png",
  },
  openGraph: {
    title: "RaidenX | The best trading terminal on SUI",
    description:
      "Fastest trading & sniping bot on SUI, 1-click buy & sell, realtime chart, audit results, limit orders, DCA, copy trade - only on RaidenX",
    images: ["https://raidenx.io/open-graph.png"],
    url: "https://raidenx.io",
  },
  twitter: {
    card: "summary_large_image",
    title: "RaidenX | The best trading terminal on SUI",
    description:
      "Fastest trading & sniping bot on SUI, 1-click buy & sell, realtime chart, audit results, limit orders, DCA, copy trade - only on RaidenX",
    images: ["https://raidenx.io/open-graph.png"],
  },
  other: {
    "msapplication-TileColor": "#da532c",
    canonical: "https://raidenx.io/",
  },
  keywords: [
    "RaidenX",
    "RaidenXTradingTerminal",
    "RaidenXTradingBot",
    "RaidenXTGBot",
    "RaidenXTelegramBot",
    "RaidenXSniperBot",
    "SuiTradingTerminal",
    "SuiTradingBot",
    "SuiMemeCoin",
    "SuiTGBot",
    "SuiTelegramBot",
    "SuiSniperBot",
    "SuiCopyTrade",
  ],
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <Script
          src="https://www.googletagmanager.com/gtag/js?id=G-DL6TJGE79R"
          strategy="afterInteractive"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-DL6TJGE79R');
          `}
        </Script>
      </head>
      <body>
        <ClearServiceWorker />
        <TurnkeyProviderWrapper>
          <AppProvider>
            <ToastContainer
              autoClose={2000}
              position="top-right"
              icon={false}
              pauseOnHover
              closeButton={false}
              hideProgressBar
              toastStyle={{
                position: "relative",
                overflow: "visible",
              }}
            />
            <BasePage>{children}</BasePage>
          </AppProvider>
        </TurnkeyProviderWrapper>
      </body>
    </html>
  );
}
